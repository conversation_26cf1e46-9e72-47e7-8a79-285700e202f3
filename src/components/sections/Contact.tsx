import React, { useState, useRef } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { Send, CheckCircle } from 'lucide-react';
import emailjs from '@emailjs/browser';
import But<PERSON> from '../ui/Button';

interface FormState {
  name: string;
  email: string;
  phone: string;
  subject: string;
  message: string;
}

interface FormErrors {
  name?: string;
  email?: string;
  phone?: string;
  subject?: string;
  message?: string;
}

const Contact: React.FC = () => {
  const [formState, setFormState] = useState<FormState>({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
  });
  
  const sectionRef = useRef<HTMLElement>(null);
  const bgRef = useRef<HTMLDivElement>(null);
  
  // Scroll-based parallax effect
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start end", "end start"]
  });
  
  // Vertical parallax movement
  const y = useTransform(scrollYProgress, [0, 1], ["-30%", "30%"]);

  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};
    
    if (!formState.name.trim()) {
      newErrors.name = 'Name is required';
    }
    
    if (!formState.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formState.email)) {
      newErrors.email = 'Email is invalid';
    }
    
    if (!formState.subject.trim()) {
      newErrors.subject = 'Subject is required';
    }
    
    if (!formState.message.trim()) {
      newErrors.message = 'Message is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormState(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user types
    if (errors[name as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      setIsSubmitting(true);
      
      try {
        // Replace these with your EmailJS service ID, template ID, and Public Key
        const result = await emailjs.send(
          'service_rm1uva9',
          'YOUR_TEMPLATE_ID',
          {
            from_name: formState.name,
            reply_to: formState.email,
            phone_number: formState.phone,
            subject: formState.subject,
            message: formState.message,
          },
          'YOUR_PUBLIC_KEY'
        );

        if (result.status === 200) {
          setIsSubmitted(true);
          // Reset form after showing success
          setTimeout(() => {
            setFormState({
              name: '',
              email: '',
              phone: '',
              subject: '',
              message: '',
            });
            setIsSubmitted(false);
          }, 3000);
        }
      } catch (error) {
        console.error('Failed to send email:', error);
        alert('Failed to send message. Please try again or contact us directly.');
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  return (
    <section
      ref={sectionRef}
      id="contact"
      className="py-12 sm:py-16 md:py-20 bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-gray-900 dark:to-gray-800 relative overflow-hidden"
    >
      {/* Mobile-optimized Background Image with Parallax Effect */}
      <motion.div
        ref={bgRef}
        className="absolute inset-0 w-full h-full bg-cover bg-center opacity-10 dark:opacity-5"
        style={{
          backgroundImage: "url('/contacts.png')",
          backgroundAttachment: window.innerWidth > 768 ? 'fixed' : 'scroll',
          y: window.innerWidth > 768 ? y : 0,
          willChange: 'transform',
        }}
      />

      {/* Enhanced mobile gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-white/85 via-white/70 to-white/85 dark:from-gray-900/95 dark:via-gray-900/90 dark:to-gray-900/95 sm:from-white/80 sm:via-transparent sm:to-white/80 dark:sm:from-gray-900/90 dark:sm:via-gray-900/95 dark:sm:to-gray-900/90" />

      <div className="container mx-auto px-3 sm:px-4 md:px-6 relative z-10">
        {/* Mobile-optimized header */}
        <div className="text-center max-w-3xl mx-auto mb-8 sm:mb-12 md:mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="text-2xl sm:text-3xl md:text-4xl font-display font-bold text-gray-900 dark:text-white mb-3 sm:mb-4 leading-tight"
          >
            Need <span className="text-primary-600 dark:text-primary-400"> Equipment?</span>
          </motion.h2>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-10 lg:gap-12 items-start">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <h3 className="text-xl sm:text-2xl font-display font-semibold text-gray-900 dark:text-white mb-4 sm:mb-6 leading-tight">
              Get in touch with us
            </h3>

            <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                    Name*
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formState.name}
                    onChange={handleChange}
                    className={`w-full px-4 py-3 sm:py-2 rounded-xl sm:rounded-lg border-2 sm:border ${
                      errors.name
                        ? 'border-error-500 dark:border-error-600'
                        : 'border-gray-300 dark:border-gray-600'
                    } bg-white/90 dark:bg-gray-800/90 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 text-base sm:text-sm`}
                    placeholder="Your name"
                  />
                  {errors.name && (
                    <motion.p
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="mt-2 text-sm text-error-600 dark:text-error-400"
                    >
                      {errors.name}
                    </motion.p>
                  )}
                </div>
                
                <div>
                  <label htmlFor="email" className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                    Email*
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formState.email}
                    onChange={handleChange}
                    className={`w-full px-4 py-3 sm:py-2 rounded-xl sm:rounded-lg border-2 sm:border ${
                      errors.email
                        ? 'border-error-500 dark:border-error-600'
                        : 'border-gray-300 dark:border-gray-600'
                    } bg-white/90 dark:bg-gray-800/90 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 text-base sm:text-sm`}
                    placeholder="<EMAIL>"
                  />
                  {errors.email && (
                    <motion.p
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="mt-2 text-sm text-error-600 dark:text-error-400"
                    >
                      {errors.email}
                    </motion.p>
                  )}
                </div>
              </div>

              <div>
                <label htmlFor="phone" className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                  Phone (optional)
                </label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formState.phone}
                  onChange={handleChange}
                  className="w-full px-4 py-3 sm:py-2 rounded-xl sm:rounded-lg border-2 sm:border border-gray-300 dark:border-gray-600 bg-white/90 dark:bg-gray-800/90 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 text-base sm:text-sm"
                  placeholder="+27(0) 456-7890"
                />
              </div>

              <div>
                <label htmlFor="subject" className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                  Subject*
                </label>
                <input
                  type="text"
                  id="subject"
                  name="subject"
                  value={formState.subject}
                  onChange={handleChange}
                  className={`w-full px-4 py-3 sm:py-2 rounded-xl sm:rounded-lg border-2 sm:border ${
                    errors.subject
                      ? 'border-error-500 dark:border-error-600'
                      : 'border-gray-300 dark:border-gray-600'
                  } bg-white/90 dark:bg-gray-800/90 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 text-base sm:text-sm`}
                  placeholder="Enter your subject"
                />
                {errors.subject && (
                  <motion.p
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mt-2 text-sm text-error-600 dark:text-error-400"
                  >
                    {errors.subject}
                  </motion.p>
                )}
              </div>

              <div>
                <label htmlFor="message" className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                  Message*
                </label>
                <textarea
                  id="message"
                  name="message"
                  value={formState.message}
                  onChange={handleChange}
                  rows={4}
                  className={`w-full px-4 py-3 sm:py-2 rounded-xl sm:rounded-lg border-2 sm:border ${
                    errors.message
                      ? 'border-error-500 dark:border-error-600'
                      : 'border-gray-300 dark:border-gray-600'
                  } bg-white/90 dark:bg-gray-800/90 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 text-base sm:text-sm resize-none`}
                  placeholder="How can we help you?"
                />
                {errors.message && (
                  <motion.p
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mt-2 text-sm text-error-600 dark:text-error-400"
                  >
                    {errors.message}
                  </motion.p>
                )}
              </div>
              
              <div>
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button
                    type="submit"
                    size="lg"
                    disabled={isSubmitting}
                    fullWidth
                    icon={isSubmitted ? <CheckCircle size={18} className="sm:w-5 sm:h-5" /> : <Send size={18} className="sm:w-5 sm:h-5" />}
                    iconPosition="left"
                    className={`py-4 sm:py-3 text-base sm:text-sm font-bold sm:font-semibold rounded-2xl sm:rounded-lg transition-all duration-300 ${
                      isSubmitted ? 'bg-success-600 hover:bg-success-700' : ''
                    }`}
                  >
                    {isSubmitting ? 'Sending...' : isSubmitted ? 'Message Sent Successfully!' : 'Send Message'}
                  </Button>
                </motion.div>
              </div>
            </form>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, ease: "easeOut" }}
            className="relative bg-white/30 dark:bg-gray-800/30 backdrop-blur-lg rounded-2xl p-0.5 overflow-hidden group"
            style={{
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
            }}
          >
            {/* Gradient border */}
            <div className="absolute inset-0 bg-gradient-to-br from-primary-400/30 via-primary-200/20 to-blue-400/30 dark:from-primary-700/30 dark:via-primary-900/20 dark:to-blue-700/30 rounded-2xl -z-10" />
            
            <div className="bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm p-6 rounded-2xl">
              <div className="flex items-center mb-6">
                <div className="p-2 bg-gradient-to-br from-primary-500 to-blue-500 rounded-lg shadow-md">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                <h3 className="ml-3 text-2xl font-display font-bold bg-gradient-to-r from-primary-600 to-blue-600 dark:from-primary-400 dark:to-blue-400 bg-clip-text text-transparent">
                  Visit Us
                </h3>
              </div>
              
              <div className="relative rounded-xl overflow-hidden mb-8 transform transition-all duration-500 group-hover:shadow-xl group-hover:scale-[1.01]">
                <div className="absolute inset-0 bg-gradient-to-br from-primary-500/10 to-blue-500/10 z-10 pointer-events-none" />
                <iframe
                  width="100%"
                  height="320"
                  loading="lazy"
                  allowFullScreen
                  src="https://www.google.com/maps?q=8+Swartberg+Road,+Alrode+South,+Alberton,+Johannesburg&output=embed"
                  className="w-full h-80 border-0 transform transition-transform duration-700 group-hover:scale-105"
                  title="Pazogen Location on Google Maps"
                  style={{
                    filter: 'saturate(1.1) contrast(1.1)',
                  }}
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 mt-1">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary-600 dark:text-primary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300">Address</h4>
                      <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                        8 Swartberg Road,<br/>
                        Alrode South, Alberton<br/>
                        Johannesburg, South Africa
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="flex-shrink-0 mt-1">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary-600 dark:text-primary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300">Phone</h4>
                      <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                        +27 (0) 10 109 6528
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 mt-1">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary-600 dark:text-primary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300">Email</h4>
                      <a href="mailto:<EMAIL>" className="block mt-1 text-sm text-primary-600 dark:text-primary-400 hover:underline transition-colors">
                        <EMAIL>
                      </a>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="flex-shrink-0 mt-1">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary-600 dark:text-primary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300">Office Hours</h4>
                      <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                        Mon - Fri: 8:00 AM - 6:00 PM<br />
                        Sat: 9:00 AM - 1:00 PM<br />
                        Sun: Closed
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                <a 
                  href="https://maps.google.com?q=8+Swartberg+Road,+Alrode+South,+Alberton,+Johannesburg" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-primary-500 to-blue-500 text-white text-sm font-medium rounded-lg hover:opacity-90 transition-opacity"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                  </svg>
                  Get Directions
                </a>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default Contact;