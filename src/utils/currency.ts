/**
 * Currency utilities for South African Rand (ZAR) formatting and conversions
 */

// Exchange rate from USD to ZAR (approximate, should be updated regularly)
const USD_TO_ZAR_RATE = 19.0;

// Estimation markup percentage for quotes
const ESTIMATION_MARKUP = 0.15; // 15%

/**
 * Format amount as South African Rand currency
 */
export const formatZAR = (amount: number): string => {
  return new Intl.NumberFormat('en-ZA', {
    style: 'currency',
    currency: 'ZAR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

/**
 * Convert USD amount to ZAR with estimation markup
 */
export const convertUSDToZARWithMarkup = (usdAmount: number): number => {
  const zarAmount = usdAmount * USD_TO_ZAR_RATE;
  return Math.round(zarAmount * (1 + ESTIMATION_MARKUP));
};

/**
 * Format budget range for display
 */
export const formatBudgetRange = (budgetKey: string): string => {
  const budgetMap: { [key: string]: string } = {
    'under-200k': 'Under R200,000',
    '200k-500k': 'R200,000 - R500,000',
    '500k-1m': 'R500,000 - R1,000,000',
    '1m-2m': 'R1,000,000 - R2,000,000',
    'over-2m': 'Over R2,000,000'
  };
  
  return budgetMap[budgetKey] || 'Not selected';
};

/**
 * Get budget range key based on ZAR amount
 */
export const getBudgetRangeKey = (zarAmount: number): string => {
  if (zarAmount < 200000) return 'under-200k';
  if (zarAmount < 500000) return '200k-500k';
  if (zarAmount < 1000000) return '500k-1m';
  if (zarAmount < 2000000) return '1m-2m';
  return 'over-2m';
};

/**
 * Currency conversion constants and multipliers
 */
export const CURRENCY_CONFIG = {
  USD_TO_ZAR_RATE,
  ESTIMATION_MARKUP,
  BUDGET_MULTIPLIERS: {
    'under-200k': 0.8,
    '200k-500k': 1.0,
    '500k-1m': 1.2,
    '1m-2m': 1.5,
    'over-2m': 2.0
  }
};
